<?php

namespace App\Services;

use App\Models\Campaign;
use App\Models\Coupon;
use App\Models\Winner;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

/**
 * Handles all draw-related operations for the LuckyDraw Engine
 *
 * Provides game show-style draw functionality including:
 * - Random winner selection
 * - Draw sequence coordination (sound/visual effects)
 * - Winner validation and processing
 */
class DrawService
{
    /**
     * Check and update all campaigns that have reached their maximum winners.
     * This sets their status to 'completed' and running to false.
     *
     * @return array Information about updated campaigns
     */
    public function checkAndUpdateCompletedCampaigns(): array
    {
        $updatedCampaigns = [];

        // Use a single query with winner counts to avoid N+1 problem
        $campaignsToCheck = Campaign::where('running', true)
            ->where('status', '!=', 'completed')
            ->withCount('winners')
            ->get();

        foreach ($campaignsToCheck as $campaign) {
            $winnerCount = $campaign->winners_count;

            // If campaign has reached or exceeded max winners, mark as completed
            if ($winnerCount >= $campaign->max_winners) {
                // Use database transaction for atomic update
                \DB::transaction(function () use ($campaign, $winnerCount, &$updatedCampaigns) {
                    $campaign->status = 'completed';
                    $campaign->running = false;
                    $campaign->save();

                    // Log the campaign completion
                    \logger()->info("Campaign ID: {$campaign->id} automatically marked as completed after reaching {$winnerCount}/{$campaign->max_winners} winners.");

                    $updatedCampaigns[] = [
                        'id' => $campaign->id,
                        'name' => $campaign->name,
                        'winner_count' => $winnerCount,
                        'max_winners' => $campaign->max_winners,
                    ];
                });
            }
        }

        return $updatedCampaigns;
    }

    /**
     * Perform a draw for the given campaign and select a winner.
     *
     * @param  Campaign  $campaign  The campaign to perform the draw for
     * @return Winner|null The winner or null if no eligible coupons
     */
    public function performDraw(Campaign $campaign): ?Winner
    {
        // Check if the campaign has reached its maximum winners limit
        $currentWinnerCount = Winner::where('campaign_id', $campaign->id)->count();

        if ($currentWinnerCount >= $campaign->max_winners) {
            throw new \Exception("This campaign has reached its maximum winner limit of {$campaign->max_winners}.");
        }

        // Start with a base query for active coupons that haven't already won
        $eligibleCouponsQuery = Coupon::where('campaign_id', $campaign->id)
            ->where('expired', false)
            ->whereDoesntHave('winner'); // Exclude coupons that have already won

        // Apply minimum transaction amount condition if set
        if ($campaign->min_transaction_amount > 0) {
            $eligibleCouponsQuery->whereHas('transaction', function ($query) use ($campaign) {
                $query->where('transaction_sum', '>=', $campaign->min_transaction_amount);
            });
        }

        // Apply purchase recency condition if set
        if ($campaign->purchase_recency_days > 0) {
            $cutoffDate = now()->subDays($campaign->purchase_recency_days);
            $eligibleCouponsQuery->whereHas('transaction', function ($query) use ($cutoffDate) {
                $query->where('transaction_date', '>=', $cutoffDate);
            });
        }

        // Get the eligible coupons
        $eligibleCoupons = $eligibleCouponsQuery->get();

        if ($eligibleCoupons->isEmpty()) {
            return null;
        }

        // Randomly select a winning coupon
        $winningCoupon = $eligibleCoupons->random();
        $transaction = $winningCoupon->transaction;

        // Create a winner record
        $winner = Winner::create([
            'campaign_id' => $campaign->id,
            'coupon_id' => $winningCoupon->id,
            'name' => $transaction->name,
            'email' => $transaction->email,
            'won_at' => Carbon::now(),
        ]);

        // Mark the winning coupon as expired to prevent it from winning again
        $winningCoupon->expired = true;
        $winningCoupon->save();

        // Expire all other coupons from the same user in this campaign
        $this->expireRelatedCoupons($winner);

        // Clear the cache for this campaign's winners and eligible coupon count
        $this->clearCampaignCache($campaign->id);

        // In performDraw method:
        $this->checkAndMarkCampaignCompleted($campaign);

        return $winner;
    }

    /**
     * Check and mark campaign as completed if max winners reached
     */
    private function checkAndMarkCampaignCompleted(Campaign $campaign): void
    {
        $winnerCount = Winner::where('campaign_id', $campaign->id)->count();
        if ($winnerCount >= $campaign->max_winners) {
            $campaign->status = 'completed';
            $campaign->running = false;
            $campaign->save();

            \logger()->info("Campaign ID: {$campaign->id} automatically marked as completed after reaching {$winnerCount}/{$campaign->max_winners} winners.");
        }
    }

    /**
     * Expire all coupons from the same user (transaction) in the same campaign.
     *
     * @param  Winner  $winner  The winner to expire related coupons for
     * @return int Number of coupons expired
     */
    public function expireRelatedCoupons(Winner $winner): int
    {
        $winningCoupon = $winner->coupon;
        $transaction = $winningCoupon->transaction;

        // Get all transaction IDs from the same user (email)
        $userTransactionIds = \App\Models\Transaction::where('email', $transaction->email)
            ->where('email', '!=', null)
            ->pluck('transaction_id');

        // Expire all other coupons from those transactions for this campaign
        // Note: The winning coupon is already marked as expired in the performDraw method
        return Coupon::whereIn('transaction_id', $userTransactionIds)
            ->where('campaign_id', $winner->campaign_id)
            ->where('id', '!=', $winningCoupon->id) // Extra safety check to not expire the winning coupon twice
            ->where('expired', false) // Only expire coupons that aren't already expired
            ->update(['expired' => true]);
    }

    /**
     * Check if a user has already won in this campaign.
     *
     * @param  string  $email  The email of the user to check
     * @param  Campaign  $campaign  The campaign to check for
     * @return bool Whether the user has already won in this campaign
     */
    public function hasUserWonInCampaign(string $email, Campaign $campaign): bool
    {
        return Winner::whereHas('coupon.transaction', function ($query) use ($email) {
            $query->where('email', $email);
        })
            ->where('campaign_id', $campaign->id)
            ->exists();
    }

    /**
     * Get eligible coupon count for a campaign.
     *
     * @param  Campaign  $campaign  The campaign to get the eligible coupon count for
     * @return int The number of eligible coupons
     */
    public function getEligibleCouponCount(Campaign $campaign): int
    {
        // Start with a base query for active coupons
        $eligibleCouponsQuery = Coupon::where('campaign_id', $campaign->id)
            ->where('expired', false)
            ->whereDoesntHave('winner'); // Exclude coupons that have already won

        // Apply minimum transaction amount condition if set
        if ($campaign->min_transaction_amount > 0) {
            $eligibleCouponsQuery->whereHas('transaction', function ($query) use ($campaign) {
                $query->where('transaction_sum', '>=', $campaign->min_transaction_amount);
            });
        }

        // Apply purchase recency condition if set
        if ($campaign->purchase_recency_days > 0) {
            $cutoffDate = now()->subDays($campaign->purchase_recency_days);
            $eligibleCouponsQuery->whereHas('transaction', function ($query) use ($cutoffDate) {
                $query->where('transaction_date', '>=', $cutoffDate);
            });
        }

        return $eligibleCouponsQuery->count();
    }

    /**
     * Get all winners for a campaign.
     *
     * @param  Campaign  $campaign  The campaign to get the winners for
     * @return \Illuminate\Database\Eloquent\Collection The winners for the campaign
     */
    public function getWinners(Campaign $campaign)
    {
        return Winner::where('campaign_id', $campaign->id)
            ->orderBy('won_at', 'desc')
            ->with(['coupon.transaction'])
            ->get();
    }

    /**
     * Clear all cache related to a campaign
     *
     * @param  int  $campaignId  The ID of the campaign to clear cache for
     */
    public function clearCampaignCache(int $campaignId): void
    {
        // Clear the eligible coupon count cache
        Cache::forget("campaign_eligible_coupons_$campaignId");

        // For paginated winners cache, we'll clear a reasonable number of pages
        // This approach is more compatible across different cache drivers
        for ($page = 1; $page <= 10; $page++) {
            Cache::forget("campaign_winners_{$campaignId}_page_$page");
        }

        // Log cache clearing for debugging purposes
        \logger()->info("Cleared cache for campaign ID: $campaignId");
    }
}
