<?php

namespace App\Services;

use App\Helpers\UniqueCoupon;
use App\Models\Campaign;
use App\Models\Coupon;
use App\Models\Transaction;
use Illuminate\Support\Collection;

/**
 * Handles coupon generation and validation logic
 *
 * Responsible for:
 * - Creating coupons based on transaction amounts
 * - Ensuring proper coupon code generation
 * - Validating coupon eligibility
 */
class CouponGenerationService
{
    /**
     * The monetary value required per coupon (Rp300,000)
     */
    const COUPON_THRESHOLD = 300000;

    /**
     * Generate coupons for a transaction based on the transaction amount.
     * Each Rp300,000 spent = 1 coupon.
     *
     * @param  Transaction  $transaction  The transaction to generate coupons for
     * @param  Campaign|\Illuminate\Database\Eloquent\Collection  $campaign  The campaign the coupons belong to
     * @return Collection Generated coupons
     */
    public function generateForTransaction(Transaction $transaction, Campaign|\Illuminate\Database\Eloquent\Collection $campaign): Collection
    {
        // Check if transaction date is within campaign period
        $transactionDate = $transaction->transaction_date;
        if ($transactionDate < $campaign->start_date || $transactionDate > $campaign->end_date) {
            return collect([]);
        }

        // Check if coupons already exist for this transaction and campaign
        $existingCount = Coupon::where('transaction_id', $transaction->transaction_id)
            ->where('campaign_id', $campaign->id)
            ->count();

        if ($existingCount > 0) {
            return collect([]);
        }

        // Calculate number of coupons to generate (1 coupon per Rp300,000)
        $couponCount = floor($transaction->transaction_sum / self::COUPON_THRESHOLD);
        $coupons = collect([]);

        // Generate the coupons
        for ($i = 0; $i < $couponCount; $i++) {
            $coupon = $this->createCoupon($transaction, $campaign);
            $coupons->push($coupon);
        }

        return $coupons;
    }

    /**
     * Creates a single coupon with a unique code
     *
     * @return Coupon The generated coupon
     */
    protected function createCoupon(Transaction $transaction, Campaign $campaign): Coupon
    {
        $coupon = Coupon::create([
            'transaction_id' => $transaction->transaction_id,
            'campaign_id' => $campaign->id,
            'code' => $this->generateCouponCode(),
            'expired' => false,
        ]);

        return $coupon;
    }

    /**
     * Generates a unique coupon code
     *
     * @return string The generated coupon code
     */
    protected function generateCouponCode(): string
    {
        return UniqueCoupon::generate();
    }

    /**
     * Generate coupons for all eligible transactions in a campaign.
     *
     * @param  Campaign  $campaign  The campaign to generate coupons for
     * @return Collection Generated coupons
     */
    public function generateForCampaign(Campaign $campaign): Collection
    {
        $transactions = Transaction::whereBetween('transaction_date', [$campaign->start_date, $campaign->end_date])->get();
        $generatedCoupons = collect([]);

        foreach ($transactions as $transaction) {
            $coupons = $this->generateForTransaction($transaction, $campaign);
            $generatedCoupons = $generatedCoupons->merge($coupons);
        }

        return $generatedCoupons;
    }

    /**
     * Get the count of coupons that would be generated for a transaction.
     *
     * @param  Transaction  $transaction  The transaction to calculate coupons for
     * @return int Number of coupons that would be generated
     */
    public function getCouponCountForTransaction(Transaction $transaction): int
    {
        return floor($transaction->transaction_sum / self::COUPON_THRESHOLD);
    }
}
