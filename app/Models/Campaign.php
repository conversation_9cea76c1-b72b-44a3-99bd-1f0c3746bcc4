<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Campaign extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'start_date',
        'end_date',
        'draw_date',
        'status',
        'running',
        'max_winners',
        'min_transaction_amount',
        'purchase_recency_days',
        'coupon_threshold',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'draw_date' => 'date',
        'running' => 'boolean',
        'max_winners' => 'integer',
        'min_transaction_amount' => 'integer',
        'purchase_recency_days' => 'integer',
        'coupon_threshold' => 'integer',
    ];

    /**
     * Get the coupons for this campaign.
     */
    public function coupons()
    {
        return $this->hasMany(Coupon::class);
    }

    /**
     * Get the winners for this campaign.
     */
    public function winners()
    {
        return $this->hasMany(Winner::class);
    }

    /**
     * Get eligible transactions for this campaign based on date range.
     */
    public function eligibleTransactions()
    {
        return Transaction::whereBetween('transaction_date', [$this->start_date, $this->end_date]);
    }

    /**
     * Get the count of eligible transactions for this campaign.
     *
     * @return int The number of transactions eligible for this campaign based on date range
     */
    public function getEligibleTransactionCountAttribute()
    {
        return $this->eligibleTransactions()->count();
    }
}
