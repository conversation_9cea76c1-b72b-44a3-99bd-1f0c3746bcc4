<?php

namespace Database\Seeders;

use App\Models\Campaign;
use App\Models\Coupon;
use App\Models\Transaction;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        // Create a default campaign for testing
        $campaign = Campaign::updateOrCreate([
            'name' => 'Test Campaign',
            'description' => 'This is a test campaign created by the seeder',
        ], [
            'start_date' => Carbon::now()->subMonth(),
            'end_date' => Carbon::now()->addMonth(),
            'draw_date' => Carbon::now()->addDays(7),
            'status' => 'active',
        ]);

        $transactions = Transaction::factory(100)->create();

        foreach ($transactions as $transaction) {
            $sum = $transaction->transaction_sum / 300000;
            $count = floor($sum);
            Coupon::factory($count)->create([
                'transaction_id' => $transaction->transaction_id,
                'campaign_id' => $campaign->id,
            ]);
        }
    }
}
